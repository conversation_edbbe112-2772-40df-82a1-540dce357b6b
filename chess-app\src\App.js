import React, { useState } from 'react';
import { StyleSheet, View, StatusBar } from 'react-native';
import { ThemeProvider } from './context/ThemeContext';
import MainMenuScreen from './screens/MainMenuScreen';
import GameScreen from './screens/GameScreen';

export default function App() {
  const [currentScreen, setCurrentScreen] = useState('menu'); // 'menu' or 'game'
  const [gameConfig, setGameConfig] = useState({ mode: null, difficulty: null });

  const handleStartGame = (mode, difficulty) => {
    setGameConfig({ mode, difficulty });
    setCurrentScreen('game');
  };

  const handleBackToMenu = () => {
    setCurrentScreen('menu');
    setGameConfig({ mode: null, difficulty: null });
  };

  return (
    <ThemeProvider>
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        {currentScreen === 'menu' ? (
          <MainMenuScreen onStartGame={handleStartGame} />
        ) : (
          <GameScreen
            gameMode={gameConfig.mode}
            difficulty={gameConfig.difficulty}
            onBackToMenu={handleBackToMenu}
          />
        )}
      </View>
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
