import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Animated,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import SettingsModal from '../components/SettingsModal';
import DifficultyModal from '../components/DifficultyModal';

const { width, height } = Dimensions.get('window');

const MainMenuScreen = ({ onStartGame }) => {
  const { theme } = useTheme();
  const [showSettings, setShowSettings] = useState(false);
  const [showDifficulty, setShowDifficulty] = useState(false);
  const [selectedMode, setSelectedMode] = useState(null);

  const handlePlayVsAI = () => {
    setSelectedMode('ai');
    setShowDifficulty(true);
  };

  const handlePlayVsFriend = () => {
    setSelectedMode('friend');
    onStartGame('friend', 'Normal');
  };

  const handleStartGame = (difficulty) => {
    setShowDifficulty(false);
    onStartGame(selectedMode, difficulty);
  };

  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={theme.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.settingsButton}
          onPress={() => setShowSettings(true)}
        >
          <Text style={styles.settingsIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      {/* Background Pattern */}
      <View style={styles.backgroundPattern}>
        {Array.from({ length: 64 }).map((_, index) => {
          const row = Math.floor(index / 8);
          const col = index % 8;
          const isLight = (row + col) % 2 === 0;
          return (
            <View
              key={index}
              style={[
                styles.patternSquare,
                {
                  backgroundColor: isLight 
                    ? 'rgba(127, 219, 218, 0.1)' 
                    : 'rgba(127, 219, 218, 0.05)',
                },
              ]}
            />
          );
        })}
      </View>

      {/* Main Content */}
      <View style={styles.content}>
        {/* Logo and Title */}
        <View style={styles.logoSection}>
          <Text style={styles.crownIcon}>👑</Text>
          <Text style={styles.title}>ChessMate</Text>
          <Text style={styles.subtitle}>The ultimate chess experience.</Text>
        </View>

        {/* Game Mode Options */}
        <View style={styles.gameModesContainer}>
          {/* Play vs AI */}
          <TouchableOpacity
            style={styles.gameModeCard}
            onPress={handlePlayVsAI}
            activeOpacity={0.8}
          >
            <View style={styles.gameModeIcon}>
              <Text style={styles.iconText}>🤖</Text>
            </View>
            <Text style={styles.gameModeTitle}>Play vs AI</Text>
            <Text style={styles.gameModeDescription}>
              Challenge our AI at various difficulty levels.
            </Text>
          </TouchableOpacity>

          {/* Play vs Friend */}
          <TouchableOpacity
            style={styles.gameModeCard}
            onPress={handlePlayVsFriend}
            activeOpacity={0.8}
          >
            <View style={styles.gameModeIcon}>
              <Text style={styles.iconText}>👥</Text>
            </View>
            <Text style={styles.gameModeTitle}>Play vs Friend</Text>
            <Text style={styles.gameModeDescription}>
              Play with a friend on the same device.
            </Text>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Press ⌘ + B to toggle the sidebar in-game.
          </Text>
        </View>
      </View>

      {/* Modals */}
      <SettingsModal
        visible={showSettings}
        onClose={() => setShowSettings(false)}
      />
      
      <DifficultyModal
        visible={showDifficulty}
        onClose={() => setShowDifficulty(false)}
        onStartGame={handleStartGame}
      />
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    zIndex: 10,
  },
  settingsButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: theme.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsIcon: {
    fontSize: 20,
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    flexWrap: 'wrap',
    opacity: 0.3,
  },
  patternSquare: {
    width: width / 8,
    height: width / 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  logoSection: {
    alignItems: 'center',
    marginTop: 40,
  },
  crownIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  title: {
    fontSize: 42,
    fontWeight: 'bold',
    color: theme.accent,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: theme.textSecondary,
    textAlign: 'center',
  },
  gameModesContainer: {
    flex: 1,
    justifyContent: 'center',
    gap: 24,
  },
  gameModeCard: {
    backgroundColor: theme.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.border,
  },
  gameModeIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: theme.accent,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconText: {
    fontSize: 28,
  },
  gameModeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  gameModeDescription: {
    fontSize: 16,
    color: theme.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  footer: {
    paddingBottom: 40,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: theme.textMuted,
    textAlign: 'center',
  },
});

export default MainMenuScreen;
