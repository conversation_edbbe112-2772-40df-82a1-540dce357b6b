import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Alert,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import ModernChessboard from '../components/ModernChessboard';
import SettingsModal from '../components/SettingsModal';

const { width, height } = Dimensions.get('window');

const GameScreen = ({ gameMode, difficulty, onBackToMenu }) => {
  const { theme } = useTheme();
  const [showSettings, setShowSettings] = useState(false);
  const [currentPlayer, setCurrentPlayer] = useState('white');
  const [gameStatus, setGameStatus] = useState('playing'); // 'playing', 'check', 'checkmate', 'draw'
  const [moveCount, setMoveCount] = useState(0);
  const [capturedPieces, setCapturedPieces] = useState({ white: [], black: [] });

  const isAIMode = gameMode === 'ai';
  const isPlayerTurn = currentPlayer === 'white' || !isAIMode;

  const handleMove = (move) => {
    setMoveCount(prev => prev + 1);
    setCurrentPlayer(prev => prev === 'white' ? 'black' : 'white');
    // Handle captured pieces, game status updates, etc.
  };

  const handleBackPress = () => {
    Alert.alert(
      'Leave Game',
      'Are you sure you want to leave the current game?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Leave', style: 'destructive', onPress: onBackToMenu },
      ]
    );
  };

  const getPlayerInfo = (color) => {
    if (color === 'white') {
      return {
        name: 'You',
        avatar: '👤',
        isActive: currentPlayer === 'white',
      };
    } else {
      return {
        name: isAIMode ? `AI (${difficulty})` : 'Player 2',
        avatar: isAIMode ? '🤖' : '👥',
        isActive: currentPlayer === 'black',
      };
    }
  };

  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={theme.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          activeOpacity={0.8}
        >
          <Text style={styles.backIcon}>←</Text>
          <Text style={styles.backText}>Menu</Text>
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <Text style={styles.crownIcon}>👑</Text>
          <Text style={styles.headerTitle}>ChessMate</Text>
        </View>
        
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={() => setShowSettings(true)}
          activeOpacity={0.8}
        >
          <Text style={styles.settingsIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      {/* Player Info - Black (Top) */}
      <View style={styles.playerInfoContainer}>
        <PlayerInfo
          player={getPlayerInfo('black')}
          capturedPieces={capturedPieces.white}
          theme={theme}
          isTop={true}
        />
      </View>

      {/* Chess Board */}
      <View style={styles.boardContainer}>
        <ModernChessboard
          onMove={handleMove}
          currentPlayer={currentPlayer}
          gameMode={gameMode}
          difficulty={difficulty}
        />
      </View>

      {/* Player Info - White (Bottom) */}
      <View style={styles.playerInfoContainer}>
        <PlayerInfo
          player={getPlayerInfo('white')}
          capturedPieces={capturedPieces.black}
          theme={theme}
          isTop={false}
        />
      </View>

      {/* Game Status */}
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          {gameStatus === 'check' && '⚠️ Check!'}
          {gameStatus === 'checkmate' && '🎉 Checkmate!'}
          {gameStatus === 'draw' && '🤝 Draw!'}
          {gameStatus === 'playing' && `Move ${Math.ceil(moveCount / 2)}`}
        </Text>
      </View>

      {/* Settings Modal */}
      <SettingsModal
        visible={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </View>
  );
};

const PlayerInfo = ({ player, capturedPieces, theme, isTop }) => {
  const styles = createPlayerInfoStyles(theme);
  
  return (
    <View style={[styles.container, isTop && styles.topPlayer]}>
      <View style={styles.playerDetails}>
        <View style={[styles.avatar, player.isActive && styles.activeAvatar]}>
          <Text style={styles.avatarText}>{player.avatar}</Text>
        </View>
        <View style={styles.playerText}>
          <Text style={[styles.playerName, player.isActive && styles.activeName]}>
            {player.name}
          </Text>
          {player.isActive && (
            <View style={styles.turnIndicator}>
              <View style={styles.turnDot} />
              <Text style={styles.turnText}>Your turn</Text>
            </View>
          )}
        </View>
      </View>
      
      {/* Captured Pieces */}
      {capturedPieces.length > 0 && (
        <View style={styles.capturedPieces}>
          {capturedPieces.map((piece, index) => (
            <Text key={index} style={styles.capturedPiece}>
              {piece}
            </Text>
          ))}
        </View>
      )}
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: theme.surface,
  },
  backIcon: {
    fontSize: 18,
    color: theme.text,
  },
  backText: {
    fontSize: 16,
    color: theme.text,
    fontWeight: '500',
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  crownIcon: {
    fontSize: 24,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.accent,
  },
  settingsButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: theme.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsIcon: {
    fontSize: 20,
  },
  playerInfoContainer: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  boardContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  statusContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    color: theme.textSecondary,
    fontWeight: '500',
  },
});

const createPlayerInfoStyles = (theme) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.surface,
    borderRadius: 12,
    padding: 16,
  },
  topPlayer: {
    transform: [{ rotate: '180deg' }],
  },
  playerDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.backgroundLight,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  activeAvatar: {
    borderColor: theme.accent,
  },
  avatarText: {
    fontSize: 20,
  },
  playerText: {
    gap: 4,
  },
  playerName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
  },
  activeName: {
    color: theme.accent,
  },
  turnIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  turnDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.accent,
  },
  turnText: {
    fontSize: 12,
    color: theme.textSecondary,
    fontWeight: '500',
  },
  capturedPieces: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  capturedPiece: {
    fontSize: 16,
  },
});

export default GameScreen;
