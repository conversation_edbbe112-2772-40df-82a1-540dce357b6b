import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Animated,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';

const { width } = Dimensions.get('window');
const BOARD_SIZE = Math.min(width - 40, 400);
const SQUARE_SIZE = BOARD_SIZE / 8;

// Chess piece Unicode symbols
const PIECES = {
  'K': '♔', 'Q': '♕', 'R': '♖', 'B': '♗', 'N': '♘', 'P': '♙', // White pieces
  'k': '♚', 'q': '♛', 'r': '♜', 'b': '♝', 'n': '♞', 'p': '♟', // Black pieces
};

// Initial chess board setup
const INITIAL_BOARD = [
  ['r', 'n', 'b', 'q', 'k', 'b', 'n', 'r'],
  ['p', 'p', 'p', 'p', 'p', 'p', 'p', 'p'],
  [null, null, null, null, null, null, null, null],
  [null, null, null, null, null, null, null, null],
  [null, null, null, null, null, null, null, null],
  [null, null, null, null, null, null, null, null],
  ['P', 'P', 'P', 'P', 'P', 'P', 'P', 'P'],
  ['R', 'N', 'B', 'Q', 'K', 'B', 'N', 'R'],
];

const ModernChessboard = ({ onMove, currentPlayer, gameMode, difficulty }) => {
  const { theme, boardColors } = useTheme();
  const [board, setBoard] = useState(INITIAL_BOARD);
  const [selectedSquare, setSelectedSquare] = useState(null);
  const [possibleMoves, setPossibleMoves] = useState([]);
  const [lastMove, setLastMove] = useState(null);

  const isWhitePiece = (piece) => piece && piece === piece.toUpperCase();
  const isBlackPiece = (piece) => piece && piece === piece.toLowerCase();

  const canSelectPiece = (piece, row, col) => {
    if (!piece) return false;
    if (currentPlayer === 'white') return isWhitePiece(piece);
    if (currentPlayer === 'black') return isBlackPiece(piece);
    return false;
  };

  const generatePossibleMoves = (piece, row, col) => {
    // Simplified move generation - in a real game, you'd implement proper chess rules
    const moves = [];
    const directions = {
      'P': [[-1, 0], [-2, 0]], // Pawn (simplified)
      'p': [[1, 0], [2, 0]],   // Pawn (simplified)
      'R': [[-1, 0], [1, 0], [0, -1], [0, 1]], // Rook
      'r': [[-1, 0], [1, 0], [0, -1], [0, 1]], // Rook
      'N': [[-2, -1], [-2, 1], [-1, -2], [-1, 2], [1, -2], [1, 2], [2, -1], [2, 1]], // Knight
      'n': [[-2, -1], [-2, 1], [-1, -2], [-1, 2], [1, -2], [1, 2], [2, -1], [2, 1]], // Knight
      'B': [[-1, -1], [-1, 1], [1, -1], [1, 1]], // Bishop
      'b': [[-1, -1], [-1, 1], [1, -1], [1, 1]], // Bishop
      'Q': [[-1, -1], [-1, 0], [-1, 1], [0, -1], [0, 1], [1, -1], [1, 0], [1, 1]], // Queen
      'q': [[-1, -1], [-1, 0], [-1, 1], [0, -1], [0, 1], [1, -1], [1, 0], [1, 1]], // Queen
      'K': [[-1, -1], [-1, 0], [-1, 1], [0, -1], [0, 1], [1, -1], [1, 0], [1, 1]], // King
      'k': [[-1, -1], [-1, 0], [-1, 1], [0, -1], [0, 1], [1, -1], [1, 0], [1, 1]], // King
    };

    const pieceDirections = directions[piece] || [];
    
    for (const [dr, dc] of pieceDirections) {
      const newRow = row + dr;
      const newCol = col + dc;
      
      if (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
        const targetPiece = board[newRow][newCol];
        
        // Can move to empty square or capture opponent piece
        if (!targetPiece || 
            (isWhitePiece(piece) && isBlackPiece(targetPiece)) ||
            (isBlackPiece(piece) && isWhitePiece(targetPiece))) {
          moves.push({ row: newRow, col: newCol });
        }
      }
    }
    
    return moves;
  };

  const handleSquarePress = (row, col) => {
    const piece = board[row][col];
    
    if (selectedSquare) {
      const { row: selectedRow, col: selectedCol } = selectedSquare;
      
      // Check if this is a valid move
      const isValidMove = possibleMoves.some(move => move.row === row && move.col === col);
      
      if (isValidMove) {
        // Make the move
        const newBoard = board.map(r => [...r]);
        newBoard[row][col] = board[selectedRow][selectedCol];
        newBoard[selectedRow][selectedCol] = null;
        
        setBoard(newBoard);
        setLastMove({ from: selectedSquare, to: { row, col } });
        setSelectedSquare(null);
        setPossibleMoves([]);
        
        // Notify parent component
        onMove({ from: selectedSquare, to: { row, col }, piece: board[selectedRow][selectedCol] });
      } else if (canSelectPiece(piece, row, col)) {
        // Select new piece
        setSelectedSquare({ row, col });
        setPossibleMoves(generatePossibleMoves(piece, row, col));
      } else {
        // Deselect
        setSelectedSquare(null);
        setPossibleMoves([]);
      }
    } else if (canSelectPiece(piece, row, col)) {
      // Select piece
      setSelectedSquare({ row, col });
      setPossibleMoves(generatePossibleMoves(piece, row, col));
    }
  };

  const isSquareSelected = (row, col) => {
    return selectedSquare && selectedSquare.row === row && selectedSquare.col === col;
  };

  const isSquarePossibleMove = (row, col) => {
    return possibleMoves.some(move => move.row === row && move.col === col);
  };

  const isSquareLastMove = (row, col) => {
    if (!lastMove) return false;
    return (lastMove.from.row === row && lastMove.from.col === col) ||
           (lastMove.to.row === row && lastMove.to.col === col);
  };

  const styles = createStyles(theme, boardColors);

  return (
    <View style={styles.container}>
      <View style={styles.board}>
        {board.map((row, rowIndex) =>
          row.map((piece, colIndex) => {
            const isLight = (rowIndex + colIndex) % 2 === 0;
            const isSelected = isSquareSelected(rowIndex, colIndex);
            const isPossibleMove = isSquarePossibleMove(rowIndex, colIndex);
            const isLastMoveSquare = isSquareLastMove(rowIndex, colIndex);
            
            return (
              <TouchableOpacity
                key={`${rowIndex}-${colIndex}`}
                style={[
                  styles.square,
                  {
                    backgroundColor: isLight ? boardColors.light : boardColors.dark,
                  },
                  isSelected && styles.selectedSquare,
                  isPossibleMove && styles.possibleMoveSquare,
                  isLastMoveSquare && styles.lastMoveSquare,
                ]}
                onPress={() => handleSquarePress(rowIndex, colIndex)}
                activeOpacity={0.8}
              >
                {piece && (
                  <Text style={[
                    styles.piece,
                    isWhitePiece(piece) ? styles.whitePiece : styles.blackPiece
                  ]}>
                    {PIECES[piece]}
                  </Text>
                )}
                {isPossibleMove && !piece && <View style={styles.moveIndicator} />}
                {isPossibleMove && piece && <View style={styles.captureIndicator} />}
              </TouchableOpacity>
            );
          })
        )}
      </View>
    </View>
  );
};

const createStyles = (theme, boardColors) => StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  board: {
    width: BOARD_SIZE,
    height: BOARD_SIZE,
    flexDirection: 'row',
    flexWrap: 'wrap',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 3,
    borderColor: theme.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 12,
  },
  square: {
    width: SQUARE_SIZE,
    height: SQUARE_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  selectedSquare: {
    backgroundColor: `${theme.accent}80`,
  },
  possibleMoveSquare: {
    backgroundColor: `${theme.possibleMove}40`,
  },
  lastMoveSquare: {
    backgroundColor: `${theme.highlight}40`,
  },
  piece: {
    fontSize: SQUARE_SIZE * 0.7,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  whitePiece: {
    color: '#FFFFFF',
  },
  blackPiece: {
    color: '#000000',
  },
  moveIndicator: {
    width: SQUARE_SIZE * 0.3,
    height: SQUARE_SIZE * 0.3,
    borderRadius: SQUARE_SIZE * 0.15,
    backgroundColor: theme.accent,
    opacity: 0.8,
  },
  captureIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    bottom: 2,
    left: 2,
    borderRadius: 8,
    borderWidth: 3,
    borderColor: theme.accent,
    opacity: 0.8,
  },
});

export default ModernChessboard;
