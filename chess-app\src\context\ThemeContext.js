import React, { createContext, useContext, useState, useEffect } from 'react';
import { themes, boardColorPresets } from '../theme/colors';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [boardColors, setBoardColors] = useState(boardColorPresets[0]);
  const [difficulty, setDifficulty] = useState('Normal');

  const theme = isDarkMode ? themes.dark : themes.light;

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const updateBoardColors = (colors) => {
    setBoardColors(colors);
  };

  const updateDifficulty = (newDifficulty) => {
    setDifficulty(newDifficulty);
  };

  const value = {
    theme,
    isDarkMode,
    toggleTheme,
    boardColors,
    updateBoardColors,
    difficulty,
    updateDifficulty,
    boardColorPresets,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
