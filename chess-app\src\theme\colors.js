// ChessMate Theme Colors
export const colors = {
  // Primary theme colors
  primary: '#4A9B8E', // Teal
  primaryDark: '#3A7A6E',
  primaryLight: '#5AABA0',
  
  // Background colors
  background: '#2C5F5D', // Dark teal background
  backgroundDark: '#1E4240',
  backgroundLight: '#3A6B69',
  
  // Surface colors
  surface: '#3A6B69',
  surfaceDark: '#2C5F5D',
  surfaceLight: '#4A7B79',
  
  // Accent colors
  accent: '#7FDBDA', // Light cyan
  accentSecondary: '#A8E6CF',
  
  // Text colors
  text: '#FFFFFF',
  textSecondary: '#B0D4D1',
  textMuted: '#8BB5B2',
  
  // Chess board colors (default)
  boardLight: '#F0D9B5', // Light squares
  boardDark: '#B58863',  // Dark squares
  
  // Alternative board colors
  boardPurpleLight: '#E6D7FF',
  boardPurpleDark: '#9B7EBD',
  boardTealLight: '#A8E6CF',
  boardTealDark: '#4A9B8E',
  
  // UI element colors
  button: '#7FDBDA',
  buttonHover: '#6FCBCA',
  buttonDisabled: '#5A9B9A',
  
  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  
  // Border colors
  border: '#5A9B9A',
  borderLight: '#7FDBDA',
  
  // Selection and highlight colors
  selected: '#7FDBDA',
  highlight: '#FFD700',
  possibleMove: 'rgba(127, 219, 218, 0.3)',
};

// Theme configurations
export const themes = {
  dark: {
    ...colors,
    isDark: true,
  },
  light: {
    ...colors,
    background: '#E8F4F3',
    backgroundDark: '#D0E8E6',
    backgroundLight: '#F0F8F7',
    surface: '#FFFFFF',
    surfaceDark: '#F5F5F5',
    text: '#2C5F5D',
    textSecondary: '#3A6B69',
    textMuted: '#5A9B9A',
    isDark: false,
  },
};

// Board color presets
export const boardColorPresets = [
  {
    name: 'Classic',
    light: '#F0D9B5',
    dark: '#B58863',
  },
  {
    name: 'Purple',
    light: '#E6D7FF',
    dark: '#9B7EBD',
  },
  {
    name: 'Teal',
    light: '#A8E6CF',
    dark: '#4A9B8E',
  },
  {
    name: 'Blue',
    light: '#E3F2FD',
    dark: '#1976D2',
  },
  {
    name: 'Green',
    light: '#E8F5E8',
    dark: '#4CAF50',
  },
];

export default colors;
