import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { useTheme } from '../context/ThemeContext';

const { width } = Dimensions.get('window');
const TILE_SIZE = (width - 32) / 8;

const Chessboard = () => {
  const { theme, boardColors } = useTheme();

  const renderBoard = () => {
    const board = [];
    for (let i = 0; i < 8; i++) {
      const row = [];
      for (let j = 0; j < 8; j++) {
        const backgroundColor = (i + j) % 2 === 0 ? boardColors.light : boardColors.dark;
        row.push(
          <View
            key={`${i}-${j}`}
            style={[styles.tile, { backgroundColor }]}
          />
        );
      }
      board.push(<View key={i} style={styles.row}>{row}</View>);
    }
    return board;
  };

  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <View style={styles.board}>
        {renderBoard()}
      </View>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  board: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: theme.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  row: {
    flexDirection: 'row',
  },
  tile: {
    width: TILE_SIZE,
    height: TILE_SIZE,
  },
});

export default Chessboard;