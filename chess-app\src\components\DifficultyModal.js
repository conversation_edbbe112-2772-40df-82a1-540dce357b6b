import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
  Animated,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';

const { width, height } = Dimensions.get('window');

const DifficultyModal = ({ visible, onClose, onStartGame }) => {
  const { theme, difficulty, updateDifficulty } = useTheme();
  const [selectedDifficulty, setSelectedDifficulty] = useState(difficulty);

  const difficulties = [
    {
      id: 'Easy',
      title: 'Easy',
      description: 'Perfect for beginners learning the game.',
      icon: '🌱',
    },
    {
      id: 'Normal',
      title: 'Normal',
      description: 'A balanced opponent that provides a good challenge.',
      icon: '⚖️',
    },
    {
      id: 'Hard',
      title: 'Hard',
      description: 'For experienced players seeking a tough opponent.',
      icon: '🔥',
    },
  ];

  const handleDifficultySelect = (difficultyId) => {
    setSelectedDifficulty(difficultyId);
  };

  const handleStartGame = () => {
    updateDifficulty(selectedDifficulty);
    onStartGame(selectedDifficulty);
  };

  const styles = createStyles(theme);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <Text style={styles.headerIcon}>🎯</Text>
            </View>
            <Text style={styles.title}>Select Difficulty</Text>
            <Text style={styles.subtitle}>Choose your opponent's skill level.</Text>
          </View>

          {/* Difficulty Options */}
          <View style={styles.optionsContainer}>
            {difficulties.map((diff) => (
              <TouchableOpacity
                key={diff.id}
                style={[
                  styles.difficultyOption,
                  selectedDifficulty === diff.id && styles.selectedOption,
                ]}
                onPress={() => handleDifficultySelect(diff.id)}
                activeOpacity={0.8}
              >
                <View style={styles.optionContent}>
                  <View style={styles.optionHeader}>
                    <Text style={styles.optionIcon}>{diff.icon}</Text>
                    <Text
                      style={[
                        styles.optionTitle,
                        selectedDifficulty === diff.id && styles.selectedText,
                      ]}
                    >
                      {diff.title}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Description */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.description}>
              {difficulties.find(d => d.id === selectedDifficulty)?.description}
            </Text>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.startButton}
              onPress={handleStartGame}
              activeOpacity={0.8}
            >
              <Text style={styles.startButtonText}>Start Game</Text>
            </TouchableOpacity>
          </View>

          {/* Close Button */}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            activeOpacity={0.8}
          >
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (theme) => StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: theme.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: theme.surface,
    borderRadius: 20,
    padding: 24,
    width: Math.min(width - 40, 400),
    maxHeight: height * 0.8,
    position: 'relative',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: theme.accent,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerIcon: {
    fontSize: 28,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.textSecondary,
    textAlign: 'center',
  },
  optionsContainer: {
    gap: 12,
    marginBottom: 24,
  },
  difficultyOption: {
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.border,
    backgroundColor: 'transparent',
    overflow: 'hidden',
  },
  selectedOption: {
    borderColor: theme.accent,
    backgroundColor: `${theme.accent}20`,
  },
  optionContent: {
    padding: 16,
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  optionIcon: {
    fontSize: 24,
  },
  optionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.text,
  },
  selectedText: {
    color: theme.accent,
  },
  descriptionContainer: {
    backgroundColor: theme.backgroundLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  description: {
    fontSize: 16,
    color: theme.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  buttonContainer: {
    gap: 12,
  },
  startButton: {
    backgroundColor: theme.accent,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.background,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.backgroundLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: theme.textMuted,
    fontWeight: 'bold',
  },
});

export default DifficultyModal;
