import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Switch,
  Dimensions,
  ScrollView,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';

const { width, height } = Dimensions.get('window');

const SettingsModal = ({ visible, onClose }) => {
  const { theme, isDarkMode, toggleTheme, boardColors, updateBoardColors, boardColorPresets } = useTheme();
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedColorType, setSelectedColorType] = useState(null); // 'light' or 'dark'

  const handleColorPresetSelect = (preset) => {
    updateBoardColors(preset);
  };

  const handleCustomColorSelect = (type) => {
    setSelectedColorType(type);
    setShowColorPicker(true);
  };

  const styles = createStyles(theme);

  const renderBoardPreview = () => {
    const squares = [];
    for (let i = 0; i < 8; i++) {
      for (let j = 0; j < 4; j++) {
        const isLight = (i + j) % 2 === 0;
        squares.push(
          <View
            key={`${i}-${j}`}
            style={[
              styles.previewSquare,
              {
                backgroundColor: isLight ? boardColors.light : boardColors.dark,
              },
            ]}
          />
        );
      }
    }
    return squares;
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Settings</Text>
            <Text style={styles.subtitle}>Customize your ChessMate experience.</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              activeOpacity={0.8}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Theme Toggle */}
            <View style={styles.settingSection}>
              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingIcon}>🌙</Text>
                  <Text style={styles.settingLabel}>Theme</Text>
                </View>
                <Switch
                  value={isDarkMode}
                  onValueChange={toggleTheme}
                  trackColor={{ false: theme.border, true: theme.accent }}
                  thumbColor={isDarkMode ? theme.background : theme.text}
                  ios_backgroundColor={theme.border}
                />
              </View>
            </View>

            {/* Board Colors Section */}
            <View style={styles.settingSection}>
              <Text style={styles.sectionTitle}>Board Colors</Text>
              
              {/* Color Selectors */}
              <View style={styles.colorSelectorRow}>
                <View style={styles.colorSelector}>
                  <Text style={styles.colorLabel}>Light Squares</Text>
                  <TouchableOpacity
                    style={[styles.colorSwatch, { backgroundColor: boardColors.light }]}
                    onPress={() => handleCustomColorSelect('light')}
                  />
                </View>
                <View style={styles.colorSelector}>
                  <Text style={styles.colorLabel}>Dark Squares</Text>
                  <TouchableOpacity
                    style={[styles.colorSwatch, { backgroundColor: boardColors.dark }]}
                    onPress={() => handleCustomColorSelect('dark')}
                  />
                </View>
              </View>

              {/* Board Preview */}
              <View style={styles.boardPreviewContainer}>
                <View style={styles.boardPreview}>
                  {renderBoardPreview()}
                </View>
              </View>

              {/* Color Presets */}
              <Text style={styles.presetsLabel}>Presets</Text>
              <View style={styles.colorPresets}>
                {boardColorPresets.map((preset, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.colorPreset,
                      boardColors.name === preset.name && styles.selectedPreset,
                    ]}
                    onPress={() => handleColorPresetSelect(preset)}
                    activeOpacity={0.8}
                  >
                    <View style={styles.presetColors}>
                      <View style={[styles.presetColor, { backgroundColor: preset.light }]} />
                      <View style={[styles.presetColor, { backgroundColor: preset.dark }]} />
                    </View>
                    <Text style={styles.presetName}>{preset.name}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (theme) => StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: theme.overlay,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: theme.surface,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    maxHeight: height * 0.85,
    paddingTop: 24,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
    position: 'relative',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.textSecondary,
  },
  closeButton: {
    position: 'absolute',
    top: 0,
    right: 24,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.backgroundLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: theme.textMuted,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  settingSection: {
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  settingIcon: {
    fontSize: 24,
  },
  settingLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 20,
  },
  colorSelectorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  colorSelector: {
    alignItems: 'center',
    gap: 8,
  },
  colorLabel: {
    fontSize: 14,
    color: theme.textSecondary,
    fontWeight: '500',
  },
  colorSwatch: {
    width: 48,
    height: 48,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: theme.border,
  },
  boardPreviewContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  boardPreview: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: 128,
    height: 128,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: theme.border,
  },
  previewSquare: {
    width: 16,
    height: 16,
  },
  presetsLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 12,
  },
  colorPresets: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorPreset: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    backgroundColor: theme.backgroundLight,
    minWidth: 80,
  },
  selectedPreset: {
    borderColor: theme.accent,
    backgroundColor: `${theme.accent}20`,
  },
  presetColors: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  presetColor: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: theme.border,
  },
  presetName: {
    fontSize: 12,
    color: theme.textSecondary,
    fontWeight: '500',
  },
});

export default SettingsModal;
